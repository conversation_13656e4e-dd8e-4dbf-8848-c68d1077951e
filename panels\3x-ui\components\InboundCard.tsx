import { Badge } from '@/components/ui/badge';
import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { InboundConfig } from '../types';
import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';

interface InboundCardProps {
  inbound: InboundConfig;
  onPress?: () => void;
  onLongPress?: () => void;
}

export default function InboundCard({ inbound, onPress, onLongPress }: InboundCardProps) {
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({}, 'border');

  // 判断配置是否有效
  const isValid = () => {
    // 检查是否启用
    if (inbound.enable === false) return false;
    
    // 检查过期时间
    if (inbound.expiryTime && inbound.expiryTime > 0) {
      const now = Date.now();
      const expiryTimestamp = inbound.expiryTime;
      if (expiryTimestamp < now) return false;
    }
    
    // 检查流量限制
    if (inbound.total && inbound.total > 0) {
      const usedTraffic = (inbound.up || 0) + (inbound.down || 0);
      if (usedTraffic >= inbound.total) return false;
    }
    
    return true;
  };

  // 获取状态badge的颜色和文本
  const getStatusBadge = () => {
    const valid = isValid();
    if (valid) {
      return {
        color:'green',
        text: '有效'
      };
    } else {
      return {
        color: 'red', // red-500
        text: '无效'
      };
    }
  };

  const statusBadge = getStatusBadge();

  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity
        style={styles.card}
        onPress={onPress}
        onLongPress={onLongPress}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: textColor }]}>
            {inbound.tag || `${inbound.protocol}-${inbound.port}`}
          </Text>
          <Badge color={statusBadge.color}>
            <Text style={styles.badgeText}>{statusBadge.text}</Text>
          </Badge>
        </View>
      </TouchableOpacity>
      <View style={[styles.divider, { backgroundColor: borderColor }]} />
    </View>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    // 不需要额外的margin，由padding控制间距
  },
  card: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  divider: {
    height: 1,
  },
});
