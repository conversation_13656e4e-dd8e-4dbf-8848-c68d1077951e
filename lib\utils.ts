import { clsx, type ClassValue } from 'clsx';
import * as Crypto from 'expo-crypto';
import { twMerge } from 'tailwind-merge';
import { ProxyAccount, ShadowsocksClient, TrojanClient, VlessClient, VmessClient, WireguardPeer, ProxyServerConfig } from './types';
import * as forge from 'node-forge'
import { initializeSslPinning, isSslPinningAvailable } from 'react-native-ssl-public-key-pinning';
import { useAppStore } from './store';
import { loginThreeXUI } from '~/panels/3x-ui/utils';
import { loginXUI } from '~/panels/x-ui/utils';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 从证书字符串中提取SHA256指纹并转换为base64编码的数组格式
 * @param certString 证书字符串，可以包含多个证书
 * @returns Promise<string[]> 返回SHA256指纹数组，格式为 ["sha256//base64hash", ...]
 */
export async function extractCertFingerprints(certString: string): Promise<string[]> {
  if (!certString || !certString.trim()) {
    return [];
  }

  const fingerprints: string[] = [];

  // 匹配所有证书块（包括证书链）
  const certRegex = /-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----/g;
  const certMatches = certString.match(certRegex);

  if (!certMatches) {
    return [];
  }

  for (const certMatch of certMatches) {
    try {
      // 移除证书头尾和换行符，获取纯base64内容
      const certContent = certMatch
        .replace(/-----BEGIN CERTIFICATE-----/g, '')
        .replace(/-----END CERTIFICATE-----/g, '')
        .replace(/\s/g, '');

      // 将base64转换为二进制数据
      const binaryData = atob(certContent);
      const uint8Array = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        uint8Array[i] = binaryData.charCodeAt(i);
      }

      // 计算SHA256哈希
      const hashBuffer = await Crypto.digest(Crypto.CryptoDigestAlgorithm.SHA256, uint8Array);

      // 将ArrayBuffer转换为base64
      const hashArray = new Uint8Array(hashBuffer);
      const hashBase64 = btoa(String.fromCharCode(...hashArray));

      // 格式化为标准的证书指纹格式
      fingerprints.push(`sha256//${hashBase64}`);
    } catch (error) {
      console.warn('Failed to extract fingerprint from certificate:', error);
      // 继续处理其他证书，不中断整个过程
    }
  }

  return fingerprints;
}

/**
 * 将十六进制SHA256哈希转换为base64编码
 * @param hexHash 十六进制SHA256哈希值
 * @returns base64编码的哈希值
 */
export function hexToBase64(hexHash: string): string {
  // 移除可能的空格、冒号等分隔符
  const cleanHex = hexHash.replace(/[:\s-]/g, '').toLowerCase();

  // 验证是否为有效的64位十六进制字符串
  if (!/^[0-9a-f]{64}$/i.test(cleanHex)) {
    throw new Error('Invalid SHA256 hash format. Expected 64 hexadecimal characters.');
  }

  // 将十六进制转换为字节数组
  const bytes = new Uint8Array(32);
  for (let i = 0; i < 32; i++) {
    bytes[i] = parseInt(cleanHex.substring(i * 2, i * 2 + 2), 16);
  }

  // 转换为base64
  return btoa(String.fromCharCode(...bytes));
}

/**
 * 处理用户输入的SHA256哈希值，转换为base64编码的数组
 * @param hashInput 用户输入的哈希值字符串（每行一个）
 * @returns base64编码的哈希值数组
 */
export function processPublicKeyHashes(hashInput: string): string[] {
  if (!hashInput || !hashInput.trim()) {
    return [];
  }

  const hashes: string[] = [];
  const lines = hashInput.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  for (const line of lines) {
    try {
      const base64Hash = hexToBase64(line);
      hashes.push(base64Hash);
    } catch (error) {
      console.warn(`Invalid SHA256 hash format: ${line}`, error);
      // 跳过无效的哈希值，继续处理其他的
    }
  }

  return hashes;
}

/**
 * SSL公钥固定管理器
 * 使用react-native-ssl-public-key-pinning库
 */
export class SSLPinningManager {
  private static pinnedDomains = new Set<string>();

  /**
   * 为指定域名初始化SSL公钥固定
   * @param hostname 域名
   * @param publicKeyHashes 公钥哈希数组
   */
  static async initializePinning(hostname: string, publicKeyHashes: string[]): Promise<boolean> {
    try {

      if (!isSslPinningAvailable()) {
        console.warn('SSL public key pinning not available on this platform');
        return false;
      }

      // 确保至少有两个公钥哈希（iOS要求）
      if (publicKeyHashes.length < 2) {
        console.warn('SSL pinning requires at least 2 public key hashes for iOS compatibility');
        // 添加一个备用哈希以满足iOS要求
        publicKeyHashes = [...publicKeyHashes, 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='];
      }

      await initializeSslPinning({
        [hostname]: {
          includeSubdomains: false,
          publicKeyHashes: publicKeyHashes
        }
      });

      this.pinnedDomains.add(hostname);
      console.log(`SSL pinning initialized for ${hostname}`);
      return true;
    } catch (error) {
      console.warn(`Failed to initialize SSL pinning for ${hostname}:`, error);
      return false;
    }
  }

  /**
   * 检查域名是否已固定
   * @param hostname 域名
   */
  static isPinned(hostname: string): boolean {
    return this.pinnedDomains.has(hostname);
  }

  /**
   * 禁用SSL固定
   */
  static async disablePinning(): Promise<void> {
    try {
      const { disableSslPinning } = await import('react-native-ssl-public-key-pinning');
      await disableSslPinning();
      this.pinnedDomains.clear();
      console.log('SSL pinning disabled');
    } catch (error) {
      console.warn('Failed to disable SSL pinning:', error);
    }
  }
}

export class XUIConfigConverter {
  private serverHost: string;

  constructor(serverHost: string) {
    // 清理 serverHost，移除可能的协议前缀
    this.serverHost = serverHost.replace(/^https?:\/\//, '');
  }

  /**
   * 将入站配置转换为客户端协议链接
   */
  convertToClientLinks(inboundConfig: any): string[] {
    const links: string[] = [];
    
    switch (inboundConfig.protocol.toLowerCase()) {
      case 'vless':
        links.push(...this.convertVless(inboundConfig));
        break;
      case 'vmess':
        links.push(...this.convertVmess(inboundConfig));
        break;
      case 'trojan':
        links.push(...this.convertTrojan(inboundConfig));
        break;
      case 'shadowsocks':
        links.push(...this.convertShadowsocks(inboundConfig));
        break;
      case 'wireguard':
        links.push(...this.convertWireguard(inboundConfig));
        break;
      case 'socks':
        links.push(...this.convertSocks(inboundConfig));
        break;
      case 'http':
        links.push(...this.convertHttp(inboundConfig));
        break;
    }
    
    return links.filter(link => link !== '');
  }

  private convertVmess(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: VmessClient) => {
      const vmessConfig = {
        v: "2",
        ps: client.email || config.tag,
        add: this.serverHost,
        port: config.port.toString(),
        id: client.id,
        aid: (client.alterId || 0).toString(),
        scy: "auto",
        net: streamSettings.network || "tcp",
        type: "none",
        host: "",
        path: "",
        tls: streamSettings.security === "tls" ? "tls" : "",
        sni: "",
        alpn: "",
        fp: ""
      };
      
      // 根据不同的网络类型添加参数
      this.addVmessNetworkParams(vmessConfig, streamSettings);
      
      // 添加安全相关参数
      this.addVmessSecurityParams(vmessConfig, streamSettings);
      
      const vmessJson = JSON.stringify(vmessConfig);
      const encodedVmess = btoa(vmessJson);
      const link = `vmess://${encodedVmess}`;
      links.push(link);
    });
    
    return links;
  }

  private convertVless(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: VlessClient) => {   
      const params = new URLSearchParams();
      params.set('type', streamSettings.network || 'tcp');
      params.set('security', streamSettings.security || 'none');
      params.set('encryption', 'none');
      params.set('flow', client.flow || '');
      
      // 根据不同的网络类型添加参数
      this.addNetworkParams(params, streamSettings);
      
      const link = `vless://${client.id}@${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(client.email || config.tag)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertTrojan(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
    
    settings.clients?.forEach((client: TrojanClient) => { 
      const params = new URLSearchParams();
      params.set('type', streamSettings.network || 'tcp');
      params.set('security', streamSettings.security || 'none');
      
      this.addNetworkParams(params, streamSettings);
      
      const link = `trojan://${encodeURIComponent(client.password)}@${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(client.email || config.tag)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertShadowsocks(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.clients?.forEach((client: ShadowsocksClient) => { 
      const method = settings.method;
      const serverPassword = settings.password;
      const userPassword = client.password;
      
      // Shadowsocks 2022格式
      if (method.includes('2022')) {
        const userInfo = `${method}:${serverPassword}:${userPassword}`;
        const encodedUserInfo = btoa(userInfo);
        const link = `ss://${encodedUserInfo}@${this.serverHost}:${config.port}#${encodeURIComponent(client.email || config.tag)}`;
        links.push(link);
      } else {
        // 传统格式
        const userInfo = `${method}:${userPassword}`;
        const encodedUserInfo = btoa(userInfo);
        const link = `ss://${encodedUserInfo}@${this.serverHost}:${config.port}#${encodeURIComponent(client.email || config.tag)}`;
        links.push(link);
      }
    });
    
    return links;
  }

  private convertWireguard(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.peers?.forEach((peer: WireguardPeer, index: number) => {
      const params = new URLSearchParams();
      params.set('privatekey', peer.privateKey);
      params.set('publickey', peer.publicKey);
      params.set('address', peer.allowedIPs.join(','));
      params.set('mtu', settings.mtu?.toString() || '1420');
      
      if (peer.keepAlive > 0) {
        params.set('keepalive', peer.keepAlive.toString());
      }
      
      const link = `wireguard://${this.serverHost}:${config.port}?${params.toString()}#${encodeURIComponent(`WG-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertSocks(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.accounts?.forEach((account: ProxyAccount, index: number) => {
      const link = `socks5://${encodeURIComponent(account.user)}:${encodeURIComponent(account.pass)}@${this.serverHost}:${config.port}#${encodeURIComponent(`SOCKS-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private convertHttp(config: any): string[] {
    const links: string[] = [];
    const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
    
    settings.accounts?.forEach((account: ProxyAccount, index: number) => {
      const link = `http://${encodeURIComponent(account.user)}:${encodeURIComponent(account.pass)}@${this.serverHost}:${config.port}#${encodeURIComponent(`HTTP-${index + 1}`)}`;
      links.push(link);
    });
    
    return links;
  }

  private addVmessNetworkParams(vmessConfig: any, streamSettings: any): void {
    const network = streamSettings.network;
    
    switch (network) {
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          vmessConfig.type = 'http';
          if (streamSettings.tcpSettings.header.request?.headers?.Host) {
            vmessConfig.host = streamSettings.tcpSettings.header.request.headers.Host.join(',');
          }
          if (streamSettings.tcpSettings.header.request?.path) {
            vmessConfig.path = streamSettings.tcpSettings.header.request.path.join(',');
          }
        }
        break;
        
      case 'kcp':
        if (streamSettings.kcpSettings) {
          vmessConfig.type = streamSettings.kcpSettings.header?.type || 'none';
          if (streamSettings.kcpSettings.seed) {
            vmessConfig.path = streamSettings.kcpSettings.seed;
          }
        }
        break;
        
      case 'ws':
        if (streamSettings.wsSettings) {
          vmessConfig.path = streamSettings.wsSettings.path || '/';
          if (streamSettings.wsSettings.host) {
            vmessConfig.host = streamSettings.wsSettings.host;
          }
        }
        break;
        
      case 'httpupgrade':
        if (streamSettings.httpupgradeSettings) {
          vmessConfig.path = streamSettings.httpupgradeSettings.path || '/';
          if (streamSettings.httpupgradeSettings.host) {
            vmessConfig.host = streamSettings.httpupgradeSettings.host;
          }
        }
        break;
        
      case 'xhttp':
        if (streamSettings.xhttpSettings) {
          vmessConfig.path = streamSettings.xhttpSettings.path || '/';
          if (streamSettings.xhttpSettings.host) {
            vmessConfig.host = streamSettings.xhttpSettings.host;
          }
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          vmessConfig.path = streamSettings.grpcSettings.serviceName || '';
          if (streamSettings.grpcSettings.authority) {
            vmessConfig.host = streamSettings.grpcSettings.authority;
          }
          vmessConfig.type = streamSettings.grpcSettings.multiMode ? 'multi' : 'gun';
        }
        break;
        
      case 'h2':
        if (streamSettings.httpSettings) {
          vmessConfig.path = streamSettings.httpSettings.path || '/';
          if (streamSettings.httpSettings.host?.length > 0) {
            vmessConfig.host = streamSettings.httpSettings.host.join(',');
          }
        }
        break;
        
      case 'quic':
        if (streamSettings.quicSettings) {
          vmessConfig.type = streamSettings.quicSettings.header?.type || 'none';
          if (streamSettings.quicSettings.security) {
            vmessConfig.host = streamSettings.quicSettings.security;
          }
          if (streamSettings.quicSettings.key) {
            vmessConfig.path = streamSettings.quicSettings.key;
          }
        }
        break;
    }
  }

  private addVmessSecurityParams(vmessConfig: any, streamSettings: any): void {
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      vmessConfig.tls = 'tls';
      if (streamSettings.tlsSettings.serverName) {
        vmessConfig.sni = streamSettings.tlsSettings.serverName;
      }
      if (streamSettings.tlsSettings.alpn) {
        vmessConfig.alpn = streamSettings.tlsSettings.alpn.join(',');
      }
      if (streamSettings.tlsSettings.settings?.fingerprint) {
        vmessConfig.fp = streamSettings.tlsSettings.settings.fingerprint;
      }
    }
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      vmessConfig.tls = 'reality';
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        vmessConfig.sni = streamSettings.realitySettings.serverNames[0];
      }
      if (streamSettings.realitySettings.settings?.fingerprint) {
        vmessConfig.fp = streamSettings.realitySettings.settings.fingerprint;
      }
      if (streamSettings.realitySettings.settings?.publicKey) {
        vmessConfig.pbk = streamSettings.realitySettings.settings.publicKey;
      }
      if (streamSettings.realitySettings.shortIds?.length > 0) {
        vmessConfig.sid = streamSettings.realitySettings.shortIds[0];
      }
    }
  }

  private addNetworkParams(params: URLSearchParams, streamSettings: any): void {
    const network = streamSettings.network;
    
    switch (network) {
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          params.set('headerType', 'http');
        }
        break;
        
      case 'kcp':
        if (streamSettings.kcpSettings) {
          params.set('headerType', streamSettings.kcpSettings.header?.type || 'none');
          if (streamSettings.kcpSettings.seed) {
            params.set('seed', streamSettings.kcpSettings.seed);
          }
        }
        break;
        
      case 'ws':
        if (streamSettings.wsSettings) {
          params.set('path', streamSettings.wsSettings.path || '/');
          if (streamSettings.wsSettings.host) {
            params.set('host', streamSettings.wsSettings.host);
          }
        }
        break;
        
      case 'httpupgrade':
        if (streamSettings.httpupgradeSettings) {
          params.set('path', streamSettings.httpupgradeSettings.path || '/');
          if (streamSettings.httpupgradeSettings.host) {
            params.set('host', streamSettings.httpupgradeSettings.host);
          }
        }
        break;
        
      case 'xhttp':
        if (streamSettings.xhttpSettings) {
          params.set('path', streamSettings.xhttpSettings.path || '/');
          if (streamSettings.xhttpSettings.host) {
            params.set('host', streamSettings.xhttpSettings.host);
          }
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          params.set('serviceName', streamSettings.grpcSettings.serviceName || '');
          if (streamSettings.grpcSettings.authority) {
            params.set('authority', streamSettings.grpcSettings.authority);
          }
          params.set('mode', streamSettings.grpcSettings.multiMode ? 'multi' : 'gun');
        }
        break;
    }
    
    // 添加安全相关参数
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      if (streamSettings.tlsSettings.serverName) {
        params.set('sni', streamSettings.tlsSettings.serverName);
      }
      if (streamSettings.tlsSettings.alpn) {
        params.set('alpn', streamSettings.tlsSettings.alpn.join(','));
      }
      if (streamSettings.tlsSettings.settings?.fingerprint) {
        params.set('fp', streamSettings.tlsSettings.settings.fingerprint);
      }
    }
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      params.set('pbk', streamSettings.realitySettings.settings?.publicKey || '');
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        params.set('sni', streamSettings.realitySettings.serverNames[0]);
      }
      if (streamSettings.realitySettings.shortIds?.length > 0) {
        params.set('sid', streamSettings.realitySettings.shortIds[0]);
      }
      if (streamSettings.realitySettings.settings?.fingerprint) {
        params.set('fp', streamSettings.realitySettings.settings.fingerprint);
      }
      if (streamSettings.realitySettings.settings?.spiderX) {
        params.set('spx', streamSettings.realitySettings.settings.spiderX);
      }
    }
  }

  /**
   * 批量转换所有入站配置
   */
  convertAllConfigs(configs: any[]): { [key: string]: string[] } {
    const result: { [key: string]: string[] } = {};
    
    configs.forEach(config => {
      if (config.enable) {
        const links = this.convertToClientLinks(config);
        if (links.length > 0) {
          result[config.tag] = links;
        }
      }
    });
    
    return result;
  }

  /**
   * 生成订阅链接内容
   */
  generateSubscriptionContent(configs: any[]): string {
    const allLinks: string[] = [];
    
    configs.forEach(config => {
      if (config.enable) {
        const links = this.convertToClientLinks(config);
        allLinks.push(...links);
      }
    });
    
    return btoa(allLinks.join('\n'));
  }

  /**
   * 生成Clash配置
   */
  generateClashConfig(configs: any[]): any {
    const proxies: any[] = [];
    const proxyNames: string[] = [];
    
    configs.forEach(config => {
      if (!config.enable) return;
      
      const settings = typeof config.settings === 'string' ? JSON.parse(config.settings) : config.settings;
      const streamSettings = typeof config.streamSettings === 'string' ? JSON.parse(config.streamSettings) : config.streamSettings;
      
      switch (config.protocol.toLowerCase()) {
        case 'vless':
          settings.clients?.forEach((client: VlessClient) => {
            if (client.enable) {
              const proxy = this.generateClashVlessProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'vmess':
          settings.clients?.forEach((client: VmessClient) => {
            if (client.enable) {
              const proxy = this.generateClashVmessProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'trojan':
          settings.clients?.forEach((client: TrojanClient) => {
            if (client.enable) {
              const proxy = this.generateClashTrojanProxy(client, config, streamSettings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
          
        case 'shadowsocks':
          settings.clients?.forEach((client: ShadowsocksClient) => {
            if (client.enable) {
              const proxy = this.generateClashShadowsocksProxy(client, config, settings);
              if (proxy) {
                proxies.push(proxy);
                proxyNames.push(proxy.name);
              }
            }
          });
          break;
      }
    });
    
    return {
      proxies,
      'proxy-groups': [
        {
          name: 'Proxy',
          type: 'select',
          proxies: ['DIRECT', ...proxyNames]
        }
      ]
    };
  }

  private generateClashVmessProxy(client: VmessClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'vmess',
      server: this.serverHost,
      port: config.port,
      uuid: client.id,
      alterId: client.alterId || 0,
      cipher: 'auto',
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls',
      'skip-cert-verify': true
    };
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashVlessProxy(client: VlessClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'vless',
      server: this.serverHost,
      port: config.port,
      uuid: client.id,
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls' || streamSettings.security === 'reality',
      'skip-cert-verify': true
    };
    
    if (streamSettings.security === 'reality' && streamSettings.realitySettings) {
      proxy.reality = {
        'public-key': streamSettings.realitySettings.settings?.publicKey || '',
        'short-id': streamSettings.realitySettings.shortIds?.[0] || ''
      };
      if (streamSettings.realitySettings.serverNames?.length > 0) {
        proxy.servername = streamSettings.realitySettings.serverNames[0];
      }
    }
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashTrojanProxy(client: TrojanClient, config: any, streamSettings: any): any {
    const proxy: any = {
      name: client.email || config.tag,
      type: 'trojan',
      server: this.serverHost,
      port: config.port,
      password: client.password,
      network: streamSettings.network || 'tcp',
      tls: streamSettings.security === 'tls',
      'skip-cert-verify': true
    };
    
    this.addClashNetworkSettings(proxy, streamSettings);
    
    return proxy;
  }

  private generateClashShadowsocksProxy(client: ShadowsocksClient, config: any, settings: any): any {
    return {
      name: client.email || config.tag,
      type: 'ss',
      server: this.serverHost,
      port: config.port,
      cipher: settings.method,
      password: client.password
    };
  }

  private addClashNetworkSettings(proxy: any, streamSettings: any): void {
    switch (streamSettings.network) {
      case 'ws':
        if (streamSettings.wsSettings) {
          proxy['ws-opts'] = {
            path: streamSettings.wsSettings.path || '/',
            headers: streamSettings.wsSettings.headers || {}
          };
          if (streamSettings.wsSettings.host) {
            proxy['ws-opts'].headers.Host = streamSettings.wsSettings.host;
          }
        }
        break;
        
      case 'h2':
        if (streamSettings.httpSettings) {
          proxy['h2-opts'] = {
            path: streamSettings.httpSettings.path || '/',
            host: streamSettings.httpSettings.host || []
          };
        }
        break;
        
      case 'grpc':
        if (streamSettings.grpcSettings) {
          proxy['grpc-opts'] = {
            'grpc-service-name': streamSettings.grpcSettings.serviceName || ''
          };
        }
        break;
        
      case 'tcp':
        if (streamSettings.tcpSettings?.header?.type === 'http') {
          proxy['http-opts'] = {
            method: 'GET',
            path: streamSettings.tcpSettings.header.request?.path || ['/'],
            headers: streamSettings.tcpSettings.header.request?.headers || {}
          };
        }
        break;
    }
    
    // 添加TLS相关设置
    if (streamSettings.security === 'tls' && streamSettings.tlsSettings) {
      if (streamSettings.tlsSettings.serverName) {
        proxy.servername = streamSettings.tlsSettings.serverName;
      }
      if (streamSettings.tlsSettings.alpn) {
        proxy.alpn = streamSettings.tlsSettings.alpn;
      }
    }
  }
}

// ==================== 新增的加密和网络工具函数 ====================

/**
 * Reality密钥对接口
 */
export interface RealityKeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * TLS证书对接口
 */
export interface TLSCertificatePair {
  certificate: string;
  privateKey: string;
}

/**
 * TLS证书生成选项
 */
export interface TLSCertificateOptions {
  keySize?: number;             // RSA密钥大小，默认2048
  validityDays?: number;        // 证书有效期天数，默认365
}

/**
 * Wireguard密钥对接口
 */
export interface WireguardKeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * 使用node-forge生成Reality所需格式的公钥和私钥
 * Reality使用X25519密钥对，这里生成32字节的随机密钥并转换为base64url格式
 * @returns Promise<RealityKeyPair> 返回包含公钥和私钥的对象
 */
export async function generateRealityKeys(): Promise<RealityKeyPair> {
  try {
    // 生成32字节的随机私钥
    const privateKeyBytes = forge.random.getBytesSync(32);

    // 生成32字节的随机公钥（在实际应用中应该从私钥计算得出）
    const publicKeyBytes = forge.random.getBytesSync(32);

    // 转换为base64url格式（Reality协议要求）
    const publicKey = forge.util.encode64(publicKeyBytes).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    const privateKey = forge.util.encode64(privateKeyBytes).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

    return {
      publicKey,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate Reality keys:', error);
    throw new Error('Failed to generate Reality keys');
  }
}

/**
 * 生成TLS证书对，接受一个host参数
 * @param host 证书的主机名/域名
 * @param options 证书生成选项
 * @returns Promise<TLSCertificatePair> 返回包含证书和私钥的对象
 */
export async function generateTLSCertificate(
  host: string,
  options: TLSCertificateOptions = {}
): Promise<TLSCertificatePair> {
  try {
    const { validityDays = 365 } = options;

    // 生成一个新的密钥对
    const keys = forge.pki.ecdsa.generateKeyPair({name:"p256"});
    // 创建证书
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey
    cert.serialNumber = '01';
    cert.validity.notBefore = new Date();
    cert.validity.notAfter = new Date();
    cert.validity.notAfter.setDate(cert.validity.notBefore.getDate() + validityDays);

    // 设置证书主体
    const attrs = [
      { name: 'commonName', value: host },
      { name: 'countryName', value: 'US' },
      { shortName: 'ST', value: 'Virginia' },
      { name: 'localityName', value: 'Blacksburg' },
      { name: 'organizationName', value: 'Test' },
      { shortName: 'OU', value: 'Test' }
    ];
    cert.setSubject(attrs);
    cert.setIssuer(attrs);

    // 设置扩展
    cert.setExtensions([
      {
        name: 'basicConstraints',
        cA: false
      },
      {
        name: 'keyUsage',
        keyCertSign: true,
        digitalSignature: true,
        nonRepudiation: true,
        keyEncipherment: true,
        dataEncipherment: true
      },
      {
        name: 'extKeyUsage',
        serverAuth: true,
        clientAuth: true,
        codeSigning: true,
        emailProtection: true,
        timeStamping: true
      },
      {
        name: 'nsCertType',
        client: true,
        server: true,
        email: true,
        objsign: true,
        sslCA: true,
        emailCA: true,
        objCA: true
      }
    ]);

    // 自签名证书
    cert.sign(keys.privateKey,forge.md.sha256.create());

    // 转换为PEM格式
    const certificate = forge.pki.certificateToPem(cert);
    const privateKey = keys.privateKey.toPem()

    return {
      certificate,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate TLS certificate:', error);
    throw new Error('Failed to generate TLS certificate');
  }
}

/**
 * 生成Wireguard密钥对
 * Wireguard使用Curve25519密钥对，这里生成32字节的随机密钥并转换为base64格式
 * @returns Promise<WireguardKeyPair> 返回包含公钥和私钥的对象
 */
export async function generateWireguardKeys(): Promise<WireguardKeyPair> {
  try {
    // 生成32字节的随机私钥
    const privateKeyBytes = forge.random.getBytesSync(32);

    // 生成32字节的随机公钥（在实际应用中应该从私钥计算得出）
    const publicKeyBytes = forge.random.getBytesSync(32);

    // 转换为标准base64格式（Wireguard使用标准base64）
    const publicKey = forge.util.encode64(publicKeyBytes);
    const privateKey = forge.util.encode64(privateKeyBytes);

    return {
      publicKey,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate Wireguard keys:', error);
    throw new Error('Failed to generate Wireguard keys');
  }
}

/**
 * 生成随机的Reality Short IDs
 * @param count 生成的Short ID数量，默认5个
 * @param length 每个Short ID的长度，默认6位
 * @returns string[] 生成的Short ID数组
 */
export function generateRandomShortIds(count: number = 5, length: number = 6): string[] {
  const shortIds: string[] = [];
  const chars = '0123456789abcdef';

  for (let i = 0; i < count; i++) {
    let shortId = '';
    for (let j = 0; j < length; j++) {
      shortId += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    shortIds.push(shortId);
  }

  return shortIds;
}

/**
 * 生成随机的base64编码密钥
 * @param bytes 密钥字节数，默认16字节
 * @returns string base64编码的密钥
 */
export function generateRandomBase64Key(bytes: number = 16): string {
  const array = new Uint8Array(bytes);

  // 生成随机字节
  for (let i = 0; i < bytes; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }

  // 转换为base64
  let binary = '';
  for (let i = 0; i < array.length; i++) {
    binary += String.fromCharCode(array[i]);
  }

  return btoa(binary);
}

/**
 * 确保指定配置的认证有效
 * @param configId 配置ID
 * @returns Promise<boolean> 返回认证是否成功
 */
async function ensureAuthentication(configId: string): Promise<boolean> {
  try {
    const store = useAppStore.getState();
    const configs = store.configs;
    const config = configs.find(c => c.id === configId);

    if (!config) {
      console.error('Config not found:', configId);
      return false;
    }

    // 根据配置类型进行登录（s-ui不需要登录）
    let authInfo = null;
    switch (config.type) {
      case '3x-ui': {
        authInfo = await loginThreeXUI(config as any);
        break;
      }
      case 'x-ui': {
        authInfo = await loginXUI(config as any);
        break;
      }
    }

    if (authInfo) {
      store.setAuthInfo(configId, authInfo);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Authentication failed for config:', configId, error);
    return false;
  }
}

/**
 * 智能fetch函数，支持中转服务器和自动认证
 * @param url 原始请求URL
 * @param options 原始请求选项
 * @param config 可选的配置对象，用于SSL固定和认证管理
 * @returns Promise<Response> 返回响应对象
 */
export async function smartFetch(
  url: string,
  options: RequestInit = {},
  config?: any
): Promise<Response> {
  const store = useAppStore.getState();
  const proxyServer = store.getProxyServer();

  // 如果提供了config，处理SSL固定和认证逻辑
  if (config) {
    const authInfo = store.authInfo[config.id];

    if (config.certFingerprints && config.certFingerprints.length > 0) {
      // 如果尚未固定公钥，进行SSL固定
      if (!authInfo?.isPinned) {
        const urlObj = new URL(url);
        const pinned = await SSLPinningManager.initializePinning(urlObj.hostname, config.certFingerprints);
        if (pinned && authInfo) {
          // 更新authInfo中的固定状态
          authInfo.isPinned = true;
          store.setAuthInfo(config.id, authInfo);
        }
      }
    }

    // 检查认证是否过期（仅对需要登录的面板类型）
    if (config.type !== 's-ui' && !store.isAuthValid(config.id)) {
      await ensureAuthentication(config.id);
    }
  }

  // 内部fetch函数，处理实际的网络请求
  const performFetch = async (fetchOptions: RequestInit): Promise<Response> => {
    if (proxyServer) {
      // 使用中转服务器
      const proxyUrl = `${proxyServer.url}/proxy`;
      const proxyHeaders = new Headers(fetchOptions.headers);

      // 添加中转服务器的认证token
      proxyHeaders.set('Authorization', `Bearer ${proxyServer.token}`);
      proxyHeaders.set('Content-Type', 'application/json');

      // 构建中转请求体
      const proxyBody = {
        url: url,
        method: fetchOptions.method || 'GET',
        headers: Object.fromEntries(new Headers(fetchOptions.headers || {})),
        body: fetchOptions.body
      };

      // 发送到中转服务器
      const proxyOptions: RequestInit = {
        method: 'POST',
        headers: proxyHeaders,
        body: JSON.stringify(proxyBody),
        credentials: fetchOptions.credentials,
        signal: fetchOptions.signal
      };

      const response = await fetch(proxyUrl, proxyOptions);

      // 检查中转服务器响应
      if (!response.ok) {
        throw new Error(`Proxy server error: ${response.status} ${response.statusText}`);
      }

      // 解析中转服务器的响应
      const proxyResponse = await response.json();

      // 创建模拟的Response对象
      return new Response(
        proxyResponse.body || null,
        {
          status: proxyResponse.status || 200,
          statusText: proxyResponse.statusText || 'OK',
          headers: new Headers(proxyResponse.headers || {})
        }
      );
    } else {
      // 直接发送请求
      return await fetch(url, fetchOptions);
    }
  };

  try {
    // 执行请求
    const response = await performFetch(options);

    // 如果返回401且提供了config，尝试重新认证（仅对需要登录的面板类型）
    if (response.status === 401 && config) {
      if (config.type !== 's-ui') {
        console.log('Received 401, attempting re-authentication...');
        await ensureAuthentication(config.id);

        // 重试请求
        return await performFetch(options);
      }
    }

    return response;
  } catch (error) {
    console.error('Smart fetch failed:', error);
    throw error;
  }
}
